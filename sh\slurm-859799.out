2025-07-16 21:39:07,296 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/emergency_fix.json
2025-07-16 21:39:07,298 [trainer.py] => prefix: emergency_fix
2025-07-16 21:39:07,298 [trainer.py] => dataset: cub
2025-07-16 21:39:07,298 [trainer.py] => memory_size: 0
2025-07-16 21:39:07,298 [trainer.py] => shuffle: True
2025-07-16 21:39:07,299 [trainer.py] => init_cls: 100
2025-07-16 21:39:07,299 [trainer.py] => increment: 10
2025-07-16 21:39:07,299 [trainer.py] => model_name: ranpac
2025-07-16 21:39:07,299 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:39:07,299 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:39:07,299 [trainer.py] => seed: 1993
2025-07-16 21:39:07,299 [trainer.py] => resume: False
2025-07-16 21:39:07,299 [trainer.py] => shot: 5
2025-07-16 21:39:07,299 [trainer.py] => use_simplecil: False
2025-07-16 21:39:07,299 [trainer.py] => tuned_epoch: 40
2025-07-16 21:39:07,300 [trainer.py] => init_lr: 0.01
2025-07-16 21:39:07,300 [trainer.py] => batch_size: 48
2025-07-16 21:39:07,300 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:39:07,300 [trainer.py] => min_lr: 0
2025-07-16 21:39:07,300 [trainer.py] => ffn_num: 64
2025-07-16 21:39:07,300 [trainer.py] => optimizer: sgd
2025-07-16 21:39:07,300 [trainer.py] => use_RP: True
2025-07-16 21:39:07,300 [trainer.py] => M: 10000
2025-07-16 21:39:07,300 [trainer.py] => fecam: False
2025-07-16 21:39:07,300 [trainer.py] => calibration: True
2025-07-16 21:39:07,300 [trainer.py] => knn_k: 5
2025-07-16 21:39:07,300 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:39:07,301 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:39:07,301 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:39:07,301 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => k_min: 3
2025-07-16 21:39:07,301 [trainer.py] => k_max: 21
2025-07-16 21:39:07,301 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:39:07,301 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 21:39:07,301 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:39:07,301 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 21:39:07,545 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
This is for the BaseNet initialization.
I'm using ViT with adapters.
_IncompatibleKeys(missing_keys=['blocks.0.adaptmlp.identity_branch.0.weight', 'blocks.0.adaptmlp.identity_branch.0.bias', 'blocks.0.adaptmlp.variation_branch.0.weight', 'blocks.0.adaptmlp.variation_branch.0.bias', 'blocks.0.adaptmlp.identity_output_proj.weight', 'blocks.0.adaptmlp.identity_output_proj.bias', 'blocks.0.adaptmlp.variation_output_proj.weight', 'blocks.0.adaptmlp.variation_output_proj.bias', 'blocks.1.adaptmlp.identity_branch.0.weight', 'blocks.1.adaptmlp.identity_branch.0.bias', 'blocks.1.adaptmlp.variation_branch.0.weight', 'blocks.1.adaptmlp.variation_branch.0.bias', 'blocks.1.adaptmlp.identity_output_proj.weight', 'blocks.1.adaptmlp.identity_output_proj.bias', 'blocks.1.adaptmlp.variation_output_proj.weight', 'blocks.1.adaptmlp.variation_output_proj.bias', 'blocks.2.adaptmlp.identity_branch.0.weight', 'blocks.2.adaptmlp.identity_branch.0.bias', 'blocks.2.adaptmlp.variation_branch.0.weight', 'blocks.2.adaptmlp.variation_branch.0.bias', 'blocks.2.adaptmlp.identity_output_proj.weight', 'blocks.2.adaptmlp.identity_output_proj.bias', 'blocks.2.adaptmlp.variation_output_proj.weight', 'blocks.2.adaptmlp.variation_output_proj.bias', 'blocks.3.adaptmlp.identity_branch.0.weight', 'blocks.3.adaptmlp.identity_branch.0.bias', 'blocks.3.adaptmlp.variation_branch.0.weight', 'blocks.3.adaptmlp.variation_branch.0.bias', 'blocks.3.adaptmlp.identity_output_proj.weight', 'blocks.3.adaptmlp.identity_output_proj.bias', 'blocks.3.adaptmlp.variation_output_proj.weight', 'blocks.3.adaptmlp.variation_output_proj.bias', 'blocks.4.adaptmlp.identity_branch.0.weight', 'blocks.4.adaptmlp.identity_branch.0.bias', 'blocks.4.adaptmlp.variation_branch.0.weight', 'blocks.4.adaptmlp.variation_branch.0.bias', 'blocks.4.adaptmlp.identity_output_proj.weight', 'blocks.4.adaptmlp.identity_output_proj.bias', 'blocks.4.adaptmlp.variation_output_proj.weight', 'blocks.4.adaptmlp.variation_output_proj.bias', 'blocks.5.adaptmlp.identity_branch.0.weight', 'blocks.5.adaptmlp.identity_branch.0.bias', 'blocks.5.adaptmlp.variation_branch.0.weight', 'blocks.5.adaptmlp.variation_branch.0.bias', 'blocks.5.adaptmlp.identity_output_proj.weight', 'blocks.5.adaptmlp.identity_output_proj.bias', 'blocks.5.adaptmlp.variation_output_proj.weight', 'blocks.5.adaptmlp.variation_output_proj.bias', 'blocks.6.adaptmlp.identity_branch.0.weight', 'blocks.6.adaptmlp.identity_branch.0.bias', 'blocks.6.adaptmlp.variation_branch.0.weight', 'blocks.6.adaptmlp.variation_branch.0.bias', 'blocks.6.adaptmlp.identity_output_proj.weight', 'blocks.6.adaptmlp.identity_output_proj.bias', 'blocks.6.adaptmlp.variation_output_proj.weight', 'blocks.6.adaptmlp.variation_output_proj.bias', 'blocks.7.adaptmlp.identity_branch.0.weight', 'blocks.7.adaptmlp.identity_branch.0.bias', 'blocks.7.adaptmlp.variation_branch.0.weight', 'blocks.7.adaptmlp.variation_branch.0.bias', 'blocks.7.adaptmlp.identity_output_proj.weight', 'blocks.7.adaptmlp.identity_output_proj.bias', 'blocks.7.adaptmlp.variation_output_proj.weight', 'blocks.7.adaptmlp.variation_output_proj.bias', 'blocks.8.adaptmlp.identity_branch.0.weight', 'blocks.8.adaptmlp.identity_branch.0.bias', 'blocks.8.adaptmlp.variation_branch.0.weight', 'blocks.8.adaptmlp.variation_branch.0.bias', 'blocks.8.adaptmlp.identity_output_proj.weight', 'blocks.8.adaptmlp.identity_output_proj.bias', 'blocks.8.adaptmlp.variation_output_proj.weight', 'blocks.8.adaptmlp.variation_output_proj.bias', 'blocks.9.adaptmlp.identity_branch.0.weight', 'blocks.9.adaptmlp.identity_branch.0.bias', 'blocks.9.adaptmlp.variation_branch.0.weight', 'blocks.9.adaptmlp.variation_branch.0.bias', 'blocks.9.adaptmlp.identity_output_proj.weight', 'blocks.9.adaptmlp.identity_output_proj.bias', 'blocks.9.adaptmlp.variation_output_proj.weight', 'blocks.9.adaptmlp.variation_output_proj.bias', 'blocks.10.adaptmlp.identity_branch.0.weight', 'blocks.10.adaptmlp.identity_branch.0.bias', 'blocks.10.adaptmlp.variation_branch.0.weight', 'blocks.10.adaptmlp.variation_branch.0.bias', 'blocks.10.adaptmlp.identity_output_proj.weight', 'blocks.10.adaptmlp.identity_output_proj.bias', 'blocks.10.adaptmlp.variation_output_proj.weight', 'blocks.10.adaptmlp.variation_output_proj.bias', 'blocks.11.adaptmlp.identity_branch.0.weight', 'blocks.11.adaptmlp.identity_branch.0.bias', 'blocks.11.adaptmlp.variation_branch.0.weight', 'blocks.11.adaptmlp.variation_branch.0.bias', 'blocks.11.adaptmlp.identity_output_proj.weight', 'blocks.11.adaptmlp.identity_output_proj.bias', 'blocks.11.adaptmlp.variation_output_proj.weight', 'blocks.11.adaptmlp.variation_output_proj.bias'], unexpected_keys=[])
After BaseNet initialization.
2025-07-16 21:39:43,391 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:39:43,393 [trainer.py] => All params: 87578880
2025-07-16 21:39:43,393 [trainer.py] => Trainable params: 1780224
2025-07-16 21:39:54,540 [ranpac.py] => Learning on 0-100
87,655,681 total parameters.
1,857,025 training parameters.
backbone.blocks.0.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.0.adaptmlp.identity_branch.0.bias 64
backbone.blocks.0.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.0.adaptmlp.variation_branch.0.bias 64
backbone.blocks.0.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.0.adaptmlp.identity_output_proj.bias 384
backbone.blocks.0.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.0.adaptmlp.variation_output_proj.bias 384
backbone.blocks.1.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.1.adaptmlp.identity_branch.0.bias 64
backbone.blocks.1.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.1.adaptmlp.variation_branch.0.bias 64
backbone.blocks.1.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.1.adaptmlp.identity_output_proj.bias 384
backbone.blocks.1.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.1.adaptmlp.variation_output_proj.bias 384
backbone.blocks.2.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.2.adaptmlp.identity_branch.0.bias 64
backbone.blocks.2.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.2.adaptmlp.variation_branch.0.bias 64
backbone.blocks.2.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.2.adaptmlp.identity_output_proj.bias 384
backbone.blocks.2.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.2.adaptmlp.variation_output_proj.bias 384
backbone.blocks.3.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.3.adaptmlp.identity_branch.0.bias 64
backbone.blocks.3.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.3.adaptmlp.variation_branch.0.bias 64
backbone.blocks.3.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.3.adaptmlp.identity_output_proj.bias 384
backbone.blocks.3.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.3.adaptmlp.variation_output_proj.bias 384
backbone.blocks.4.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.4.adaptmlp.identity_branch.0.bias 64
backbone.blocks.4.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.4.adaptmlp.variation_branch.0.bias 64
backbone.blocks.4.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.4.adaptmlp.identity_output_proj.bias 384
backbone.blocks.4.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.4.adaptmlp.variation_output_proj.bias 384
backbone.blocks.5.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.5.adaptmlp.identity_branch.0.bias 64
backbone.blocks.5.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.5.adaptmlp.variation_branch.0.bias 64
backbone.blocks.5.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.5.adaptmlp.identity_output_proj.bias 384
backbone.blocks.5.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.5.adaptmlp.variation_output_proj.bias 384
backbone.blocks.6.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.6.adaptmlp.identity_branch.0.bias 64
backbone.blocks.6.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.6.adaptmlp.variation_branch.0.bias 64
backbone.blocks.6.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.6.adaptmlp.identity_output_proj.bias 384
backbone.blocks.6.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.6.adaptmlp.variation_output_proj.bias 384
backbone.blocks.7.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.7.adaptmlp.identity_branch.0.bias 64
backbone.blocks.7.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.7.adaptmlp.variation_branch.0.bias 64
backbone.blocks.7.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.7.adaptmlp.identity_output_proj.bias 384
backbone.blocks.7.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.7.adaptmlp.variation_output_proj.bias 384
backbone.blocks.8.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.8.adaptmlp.identity_branch.0.bias 64
backbone.blocks.8.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.8.adaptmlp.variation_branch.0.bias 64
backbone.blocks.8.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.8.adaptmlp.identity_output_proj.bias 384
backbone.blocks.8.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.8.adaptmlp.variation_output_proj.bias 384
backbone.blocks.9.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.9.adaptmlp.identity_branch.0.bias 64
backbone.blocks.9.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.9.adaptmlp.variation_branch.0.bias 64
backbone.blocks.9.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.9.adaptmlp.identity_output_proj.bias 384
backbone.blocks.9.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.9.adaptmlp.variation_output_proj.bias 384
backbone.blocks.10.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.10.adaptmlp.identity_branch.0.bias 64
backbone.blocks.10.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.10.adaptmlp.variation_branch.0.bias 64
backbone.blocks.10.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.10.adaptmlp.identity_output_proj.bias 384
backbone.blocks.10.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.10.adaptmlp.variation_output_proj.bias 384
backbone.blocks.11.adaptmlp.identity_branch.0.weight 49152
backbone.blocks.11.adaptmlp.identity_branch.0.bias 64
backbone.blocks.11.adaptmlp.variation_branch.0.weight 49152
backbone.blocks.11.adaptmlp.variation_branch.0.bias 64
backbone.blocks.11.adaptmlp.identity_output_proj.weight 24576
backbone.blocks.11.adaptmlp.identity_output_proj.bias 384
backbone.blocks.11.adaptmlp.variation_output_proj.weight 24576
backbone.blocks.11.adaptmlp.variation_output_proj.bias 384
fc.weight 76800
fc.sigma 1

  0%|          | 0/40 [00:00<?, ?it/s]
Task 0, Epoch 1/40 => Loss 16.591, Disentangle_Loss 120.000, Train_accy 3.17, Test_accy 8.56:   0%|          | 0/40 [01:38<?, ?it/s]
Task 0, Epoch 1/40 => Loss 16.591, Disentangle_Loss 120.000, Train_accy 3.17, Test_accy 8.56:   2%|▎         | 1/40 [01:38<1:03:46, 98.11s/it]
Task 0, Epoch 2/40 => Loss 17.804, Disentangle_Loss 132.745, Train_accy 19.29, Test_accy 41.21:   2%|▎         | 1/40 [02:31<1:03:46, 98.11s/it]
Task 0, Epoch 2/40 => Loss 17.804, Disentangle_Loss 132.745, Train_accy 19.29, Test_accy 41.21:   5%|▌         | 2/40 [02:31<45:28, 71.79s/it]  
Task 0, Epoch 3/40 => Loss 18.918, Disentangle_Loss 145.361, Train_accy 47.15, Test_accy 65.13:   5%|▌         | 2/40 [03:17<45:28, 71.79s/it]
Task 0, Epoch 3/40 => Loss 18.918, Disentangle_Loss 145.361, Train_accy 47.15, Test_accy 65.13:   8%|▊         | 3/40 [03:17<37:06, 60.19s/it]
Task 0, Epoch 4/40 => Loss 19.777, Disentangle_Loss 157.822, Train_accy 64.70, Test_accy 76.95:   8%|▊         | 3/40 [04:03<37:06, 60.19s/it]
Task 0, Epoch 4/40 => Loss 19.777, Disentangle_Loss 157.822, Train_accy 64.70, Test_accy 76.95:  10%|█         | 4/40 [04:03<32:41, 54.48s/it]
Task 0, Epoch 5/40 => Loss 20.180, Disentangle_Loss 170.468, Train_accy 73.41, Test_accy 82.95:  10%|█         | 4/40 [04:52<32:41, 54.48s/it]
Task 0, Epoch 5/40 => Loss 20.180, Disentangle_Loss 170.468, Train_accy 73.41, Test_accy 82.95:  12%|█▎        | 5/40 [04:52<30:41, 52.62s/it]
Task 0, Epoch 6/40 => Loss 20.303, Disentangle_Loss 183.087, Train_accy 78.45, Test_accy 86.86:  12%|█▎        | 5/40 [05:43<30:41, 52.62s/it]
Task 0, Epoch 6/40 => Loss 20.303, Disentangle_Loss 183.087, Train_accy 78.45, Test_accy 86.86:  15%|█▌        | 6/40 [05:43<29:27, 51.98s/it]
Task 0, Epoch 7/40 => Loss 20.835, Disentangle_Loss 195.700, Train_accy 82.68, Test_accy 88.11:  15%|█▌        | 6/40 [06:35<29:27, 51.98s/it]
Task 0, Epoch 7/40 => Loss 20.835, Disentangle_Loss 195.700, Train_accy 82.68, Test_accy 88.11:  18%|█▊        | 7/40 [06:35<28:30, 51.85s/it]
Task 0, Epoch 8/40 => Loss 21.826, Disentangle_Loss 208.314, Train_accy 84.08, Test_accy 89.50:  18%|█▊        | 7/40 [07:20<28:30, 51.85s/it]
Task 0, Epoch 8/40 => Loss 21.826, Disentangle_Loss 208.314, Train_accy 84.08, Test_accy 89.50:  20%|██        | 8/40 [07:20<26:28, 49.63s/it]
Task 0, Epoch 9/40 => Loss 22.931, Disentangle_Loss 220.957, Train_accy 84.85, Test_accy 90.02:  20%|██        | 8/40 [08:05<26:28, 49.63s/it]
Task 0, Epoch 9/40 => Loss 22.931, Disentangle_Loss 220.957, Train_accy 84.85, Test_accy 90.02:  22%|██▎       | 9/40 [08:05<24:59, 48.36s/it]
Task 0, Epoch 10/40 => Loss 24.104, Disentangle_Loss 233.528, Train_accy 86.22, Test_accy 90.02:  22%|██▎       | 9/40 [08:48<24:59, 48.36s/it]
Task 0, Epoch 10/40 => Loss 24.104, Disentangle_Loss 233.528, Train_accy 86.22, Test_accy 90.02:  25%|██▌       | 10/40 [08:48<23:17, 46.57s/it]
Task 0, Epoch 11/40 => Loss 25.330, Disentangle_Loss 246.102, Train_accy 85.92, Test_accy 90.78:  25%|██▌       | 10/40 [09:35<23:17, 46.57s/it]
Task 0, Epoch 11/40 => Loss 25.330, Disentangle_Loss 246.102, Train_accy 85.92, Test_accy 90.78:  28%|██▊       | 11/40 [09:35<22:40, 46.91s/it]
Task 0, Epoch 12/40 => Loss 25.060, Disentangle_Loss 244.009, Train_accy 86.92, Test_accy 90.99:  28%|██▊       | 11/40 [10:20<22:40, 46.91s/it]
Task 0, Epoch 12/40 => Loss 25.060, Disentangle_Loss 244.009, Train_accy 86.92, Test_accy 90.99:  30%|███       | 12/40 [10:20<21:37, 46.34s/it]
Task 0, Epoch 13/40 => Loss 24.775, Disentangle_Loss 241.896, Train_accy 88.62, Test_accy 91.06:  30%|███       | 12/40 [11:06<21:37, 46.34s/it]
Task 0, Epoch 13/40 => Loss 24.775, Disentangle_Loss 241.896, Train_accy 88.62, Test_accy 91.06:  32%|███▎      | 13/40 [11:06<20:41, 45.97s/it]
Task 0, Epoch 14/40 => Loss 24.530, Disentangle_Loss 239.765, Train_accy 89.26, Test_accy 91.51:  32%|███▎      | 13/40 [11:55<20:41, 45.97s/it]
Task 0, Epoch 14/40 => Loss 24.530, Disentangle_Loss 239.765, Train_accy 89.26, Test_accy 91.51:  35%|███▌      | 14/40 [11:55<20:19, 46.90s/it]
Task 0, Epoch 15/40 => Loss 24.331, Disentangle_Loss 237.691, Train_accy 88.59, Test_accy 91.30:  35%|███▌      | 14/40 [12:38<20:19, 46.90s/it]
Task 0, Epoch 15/40 => Loss 24.331, Disentangle_Loss 237.691, Train_accy 88.59, Test_accy 91.30:  38%|███▊      | 15/40 [12:38<19:04, 45.77s/it]
Task 0, Epoch 16/40 => Loss 24.088, Disentangle_Loss 235.516, Train_accy 89.72, Test_accy 91.58:  38%|███▊      | 15/40 [13:19<19:04, 45.77s/it]
Task 0, Epoch 16/40 => Loss 24.088, Disentangle_Loss 235.516, Train_accy 89.72, Test_accy 91.58:  40%|████      | 16/40 [13:19<17:47, 44.48s/it]
Task 0, Epoch 17/40 => Loss 23.864, Disentangle_Loss 233.404, Train_accy 89.46, Test_accy 91.89:  40%|████      | 16/40 [14:05<17:47, 44.48s/it]
Task 0, Epoch 17/40 => Loss 23.864, Disentangle_Loss 233.404, Train_accy 89.46, Test_accy 91.89:  42%|████▎     | 17/40 [14:05<17:08, 44.71s/it]
Task 0, Epoch 18/40 => Loss 23.643, Disentangle_Loss 231.214, Train_accy 90.02, Test_accy 91.92:  42%|████▎     | 17/40 [14:50<17:08, 44.71s/it]
Task 0, Epoch 18/40 => Loss 23.643, Disentangle_Loss 231.214, Train_accy 90.02, Test_accy 91.92:  45%|████▌     | 18/40 [14:50<16:28, 44.94s/it]
Task 0, Epoch 19/40 => Loss 23.381, Disentangle_Loss 228.926, Train_accy 90.16, Test_accy 91.99:  45%|████▌     | 18/40 [15:38<16:28, 44.94s/it]
Task 0, Epoch 19/40 => Loss 23.381, Disentangle_Loss 228.926, Train_accy 90.16, Test_accy 91.99:  48%|████▊     | 19/40 [15:38<16:00, 45.73s/it]
Task 0, Epoch 20/40 => Loss 23.170, Disentangle_Loss 226.534, Train_accy 89.49, Test_accy 91.96:  48%|████▊     | 19/40 [16:23<16:00, 45.73s/it]
Task 0, Epoch 20/40 => Loss 23.170, Disentangle_Loss 226.534, Train_accy 89.49, Test_accy 91.96:  50%|█████     | 20/40 [16:23<15:14, 45.72s/it]
Task 0, Epoch 21/40 => Loss 22.925, Disentangle_Loss 224.364, Train_accy 90.49, Test_accy 92.17:  50%|█████     | 20/40 [17:08<15:14, 45.72s/it]
Task 0, Epoch 21/40 => Loss 22.925, Disentangle_Loss 224.364, Train_accy 90.49, Test_accy 92.17:  52%|█████▎    | 21/40 [17:08<14:23, 45.47s/it]
Task 0, Epoch 22/40 => Loss 22.703, Disentangle_Loss 222.268, Train_accy 90.79, Test_accy 92.03:  52%|█████▎    | 21/40 [17:53<14:23, 45.47s/it]
Task 0, Epoch 22/40 => Loss 22.703, Disentangle_Loss 222.268, Train_accy 90.79, Test_accy 92.03:  55%|█████▌    | 22/40 [17:53<13:32, 45.16s/it]
Task 0, Epoch 23/40 => Loss 22.495, Disentangle_Loss 220.185, Train_accy 89.99, Test_accy 92.10:  55%|█████▌    | 22/40 [18:44<13:32, 45.16s/it]
Task 0, Epoch 23/40 => Loss 22.495, Disentangle_Loss 220.185, Train_accy 89.99, Test_accy 92.10:  57%|█████▊    | 23/40 [18:44<13:21, 47.17s/it]
Task 0, Epoch 24/40 => Loss 22.254, Disentangle_Loss 218.086, Train_accy 91.49, Test_accy 92.10:  57%|█████▊    | 23/40 [19:30<13:21, 47.17s/it]
Task 0, Epoch 24/40 => Loss 22.254, Disentangle_Loss 218.086, Train_accy 91.49, Test_accy 92.10:  60%|██████    | 24/40 [19:30<12:24, 46.55s/it]
Task 0, Epoch 25/40 => Loss 22.049, Disentangle_Loss 215.996, Train_accy 90.82, Test_accy 92.24:  60%|██████    | 24/40 [20:18<12:24, 46.55s/it]
Task 0, Epoch 25/40 => Loss 22.049, Disentangle_Loss 215.996, Train_accy 90.82, Test_accy 92.24:  62%|██████▎   | 25/40 [20:18<11:48, 47.26s/it]
Task 0, Epoch 26/40 => Loss 21.856, Disentangle_Loss 213.908, Train_accy 90.56, Test_accy 92.31:  62%|██████▎   | 25/40 [21:11<11:48, 47.26s/it]
Task 0, Epoch 26/40 => Loss 21.856, Disentangle_Loss 213.908, Train_accy 90.56, Test_accy 92.31:  65%|██████▌   | 26/40 [21:11<11:22, 48.75s/it]
Task 0, Epoch 27/40 => Loss 21.640, Disentangle_Loss 211.819, Train_accy 90.52, Test_accy 92.13:  65%|██████▌   | 26/40 [21:56<11:22, 48.75s/it]
Task 0, Epoch 27/40 => Loss 21.640, Disentangle_Loss 211.819, Train_accy 90.52, Test_accy 92.13:  68%|██████▊   | 27/40 [21:56<10:22, 47.85s/it]
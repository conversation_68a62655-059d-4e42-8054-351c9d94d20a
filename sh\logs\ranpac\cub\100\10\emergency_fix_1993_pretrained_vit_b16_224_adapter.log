2025-07-16 21:39:07,296 [trainer.py] => config: /home/<USER>/workdir/FSCIL-Calibration-main/exps/emergency_fix.json
2025-07-16 21:39:07,298 [trainer.py] => prefix: emergency_fix
2025-07-16 21:39:07,298 [trainer.py] => dataset: cub
2025-07-16 21:39:07,298 [trainer.py] => memory_size: 0
2025-07-16 21:39:07,298 [trainer.py] => shuffle: True
2025-07-16 21:39:07,299 [trainer.py] => init_cls: 100
2025-07-16 21:39:07,299 [trainer.py] => increment: 10
2025-07-16 21:39:07,299 [trainer.py] => model_name: ranpac
2025-07-16 21:39:07,299 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:39:07,299 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:39:07,299 [trainer.py] => seed: 1993
2025-07-16 21:39:07,299 [trainer.py] => resume: False
2025-07-16 21:39:07,299 [trainer.py] => shot: 5
2025-07-16 21:39:07,299 [trainer.py] => use_simplecil: False
2025-07-16 21:39:07,299 [trainer.py] => tuned_epoch: 40
2025-07-16 21:39:07,300 [trainer.py] => init_lr: 0.01
2025-07-16 21:39:07,300 [trainer.py] => batch_size: 48
2025-07-16 21:39:07,300 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:39:07,300 [trainer.py] => min_lr: 0
2025-07-16 21:39:07,300 [trainer.py] => ffn_num: 64
2025-07-16 21:39:07,300 [trainer.py] => optimizer: sgd
2025-07-16 21:39:07,300 [trainer.py] => use_RP: True
2025-07-16 21:39:07,300 [trainer.py] => M: 10000
2025-07-16 21:39:07,300 [trainer.py] => fecam: False
2025-07-16 21:39:07,300 [trainer.py] => calibration: True
2025-07-16 21:39:07,300 [trainer.py] => knn_k: 5
2025-07-16 21:39:07,300 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:39:07,301 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:39:07,301 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:39:07,301 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => k_min: 3
2025-07-16 21:39:07,301 [trainer.py] => k_max: 21
2025-07-16 21:39:07,301 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:39:07,301 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:39:07,301 [trainer.py] => _comment_emergency: === EMERGENCY FIX: 完全禁用解耦适配器 ===
2025-07-16 21:39:07,301 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:39:07,301 [trainer.py] => _comment_note: 此配置文件用于紧急修复性能下降问题，完全禁用解耦适配器功能
2025-07-16 21:39:07,545 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:39:43,391 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.1
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Missing parameters: ['identity_bottleneck', 'variation_bottleneck', 'disentangle_loss_weight', 'variation_cov_weight'], using defaults
2025-07-16 21:39:43,392 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:39:43,393 [trainer.py] => All params: 87578880
2025-07-16 21:39:43,393 [trainer.py] => Trainable params: 1780224
2025-07-16 21:39:54,540 [ranpac.py] => Learning on 0-100

2025-07-16 21:47:21,058 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-16 21:47:21,058 [trainer.py] => prefix: disentangled_reproduce
2025-07-16 21:47:21,059 [trainer.py] => dataset: cub
2025-07-16 21:47:21,059 [trainer.py] => memory_size: 0
2025-07-16 21:47:21,059 [trainer.py] => shuffle: True
2025-07-16 21:47:21,059 [trainer.py] => init_cls: 100
2025-07-16 21:47:21,059 [trainer.py] => increment: 10
2025-07-16 21:47:21,059 [trainer.py] => model_name: ranpac
2025-07-16 21:47:21,059 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:47:21,060 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:47:21,060 [trainer.py] => seed: 1993
2025-07-16 21:47:21,060 [trainer.py] => resume: False
2025-07-16 21:47:21,060 [trainer.py] => shot: 5
2025-07-16 21:47:21,060 [trainer.py] => use_simplecil: False
2025-07-16 21:47:21,060 [trainer.py] => tuned_epoch: 40
2025-07-16 21:47:21,060 [trainer.py] => init_lr: 0.01
2025-07-16 21:47:21,060 [trainer.py] => batch_size: 48
2025-07-16 21:47:21,061 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:47:21,061 [trainer.py] => min_lr: 0
2025-07-16 21:47:21,061 [trainer.py] => ffn_num: 64
2025-07-16 21:47:21,061 [trainer.py] => optimizer: sgd
2025-07-16 21:47:21,061 [trainer.py] => use_RP: True
2025-07-16 21:47:21,061 [trainer.py] => M: 10000
2025-07-16 21:47:21,061 [trainer.py] => fecam: False
2025-07-16 21:47:21,061 [trainer.py] => calibration: True
2025-07-16 21:47:21,061 [trainer.py] => knn_k: 5
2025-07-16 21:47:21,062 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:47:21,062 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:47:21,062 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:47:21,062 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:47:21,062 [trainer.py] => k_min: 3
2025-07-16 21:47:21,062 [trainer.py] => k_max: 21
2025-07-16 21:47:21,062 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:47:21,062 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:47:21,063 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-16 21:47:21,063 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:47:21,063 [trainer.py] => identity_bottleneck: 64
2025-07-16 21:47:21,063 [trainer.py] => variation_bottleneck: 64
2025-07-16 21:47:21,063 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-16 21:47:21,063 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-16 21:47:21,063 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-16 21:47:21,063 [trainer.py] => decorrelation_weight: 0.1
2025-07-16 21:47:21,064 [trainer.py] => orthogonal_weight: 0.01
2025-07-16 21:47:21,064 [trainer.py] => mutual_info_weight: 0.05
2025-07-16 21:47:21,064 [trainer.py] => contrastive_weight: 0.03
2025-07-16 21:47:21,064 [trainer.py] => disentangle_temperature: 0.1
2025-07-16 21:47:21,064 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-16 21:47:21,064 [trainer.py] => orthogonal_constraint: True
2025-07-16 21:47:21,064 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-16 21:47:21,064 [trainer.py] => variation_cov_weight: 0.8
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_weight: 1.0
2025-07-16 21:47:21,065 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-16 21:47:21,065 [trainer.py] => min_samples_for_cov: 5
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_metric: cosine
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-16 21:47:21,065 [trainer.py] => identity_similarity_top_k: 5
2025-07-16 21:47:21,200 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:47:52,235 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:47:52,236 [trainer.py] => All params: 87578880
2025-07-16 21:47:52,237 [trainer.py] => Trainable params: 1780224
2025-07-16 21:47:53,646 [ranpac.py] => Learning on 0-100
2025-07-16 21:50:26,185 [trainer.py] => config: ./exps/ranpac_disentangled.json
2025-07-16 21:50:26,186 [trainer.py] => prefix: disentangled_reproduce
2025-07-16 21:50:26,187 [trainer.py] => dataset: cub
2025-07-16 21:50:26,187 [trainer.py] => memory_size: 0
2025-07-16 21:50:26,187 [trainer.py] => shuffle: True
2025-07-16 21:50:26,188 [trainer.py] => init_cls: 100
2025-07-16 21:50:26,188 [trainer.py] => increment: 10
2025-07-16 21:50:26,189 [trainer.py] => model_name: ranpac
2025-07-16 21:50:26,189 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-16 21:50:26,189 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-16 21:50:26,190 [trainer.py] => seed: 1993
2025-07-16 21:50:26,190 [trainer.py] => resume: False
2025-07-16 21:50:26,191 [trainer.py] => shot: 5
2025-07-16 21:50:26,191 [trainer.py] => use_simplecil: False
2025-07-16 21:50:26,191 [trainer.py] => tuned_epoch: 1
2025-07-16 21:50:26,192 [trainer.py] => init_lr: 0.01
2025-07-16 21:50:26,192 [trainer.py] => batch_size: 48
2025-07-16 21:50:26,192 [trainer.py] => weight_decay: 0.0005
2025-07-16 21:50:26,193 [trainer.py] => min_lr: 0
2025-07-16 21:50:26,193 [trainer.py] => ffn_num: 64
2025-07-16 21:50:26,193 [trainer.py] => optimizer: sgd
2025-07-16 21:50:26,193 [trainer.py] => use_RP: True
2025-07-16 21:50:26,194 [trainer.py] => M: 10000
2025-07-16 21:50:26,194 [trainer.py] => fecam: False
2025-07-16 21:50:26,194 [trainer.py] => calibration: True
2025-07-16 21:50:26,195 [trainer.py] => knn_k: 5
2025-07-16 21:50:26,195 [trainer.py] => knn_distance_metric: cosine
2025-07-16 21:50:26,195 [trainer.py] => knn_weight_decay: 0.1
2025-07-16 21:50:26,196 [trainer.py] => knn_adaptive_k: True
2025-07-16 21:50:26,196 [trainer.py] => knn_temperature: 16.0
2025-07-16 21:50:26,196 [trainer.py] => k_min: 3
2025-07-16 21:50:26,196 [trainer.py] => k_max: 21
2025-07-16 21:50:26,197 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-16 21:50:26,197 [trainer.py] => cosine_temperature: 16.0
2025-07-16 21:50:26,197 [trainer.py] => _comment_disentangled: === Disentangled Adapter Configuration ===
2025-07-16 21:50:26,197 [trainer.py] => use_disentangled_adapter: True
2025-07-16 21:50:26,198 [trainer.py] => identity_bottleneck: 64
2025-07-16 21:50:26,198 [trainer.py] => variation_bottleneck: 64
2025-07-16 21:50:26,198 [trainer.py] => _comment_disentangle_loss: === Disentanglement Loss Configuration ===
2025-07-16 21:50:26,199 [trainer.py] => disentangle_loss_weight: 0.001
2025-07-16 21:50:26,199 [trainer.py] => use_adaptive_disentangle_loss: True
2025-07-16 21:50:26,199 [trainer.py] => decorrelation_weight: 0.1
2025-07-16 21:50:26,199 [trainer.py] => orthogonal_weight: 0.01
2025-07-16 21:50:26,200 [trainer.py] => mutual_info_weight: 0.05
2025-07-16 21:50:26,200 [trainer.py] => contrastive_weight: 0.03
2025-07-16 21:50:26,200 [trainer.py] => disentangle_temperature: 0.1
2025-07-16 21:50:26,201 [trainer.py] => disentangle_warmup_epochs: 10
2025-07-16 21:50:26,201 [trainer.py] => orthogonal_constraint: True
2025-07-16 21:50:26,201 [trainer.py] => _comment_covariance: === Disentangled Covariance Configuration ===
2025-07-16 21:50:26,204 [trainer.py] => variation_cov_weight: 0.8
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_weight: 1.0
2025-07-16 21:50:26,204 [trainer.py] => cov_regularization_strength: 1e-06
2025-07-16 21:50:26,204 [trainer.py] => min_samples_for_cov: 5
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_metric: cosine
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_temperature: 16.0
2025-07-16 21:50:26,204 [trainer.py] => identity_similarity_top_k: 5
2025-07-16 21:50:26,482 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Enabled with loss_weight=0.001
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Initialized with adaptive=True
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Covariance calibrator initialized
2025-07-16 21:50:46,691 [ranpac.py] => [Disentangled Adapter] Configuration validation completed
2025-07-16 21:50:46,692 [trainer.py] => All params: 87578880
2025-07-16 21:50:46,693 [trainer.py] => Trainable params: 1780224
2025-07-16 21:50:48,122 [ranpac.py] => Learning on 0-100
2025-07-16 21:51:00,963 [ranpac.py] => [Disentangled Training] Error in disentangled forward pass: CUDA out of memory. Tried to allocate 112.00 MiB (GPU 0; 21.99 GiB total capacity; 2.37 GiB already allocated; 44.81 MiB free; 2.51 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF, falling back to normal training
